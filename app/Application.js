Ext.define('MyAppName.Application', {
    extend: 'Ext.app.Application',
    
    name: 'MyAppName',
    quickTips: false,
    platformConfig: {
        desktop: {
            quickTips: true
        }
    },
    
    // 3. FunctionDeclarationTree & FormalParameterListTree
    launch: function(profile) {

        const weak = new WeakMap();

        const key = Symbol('my ref');
        const someObject = { a:1 };

        weak.set(key, someObject);
        console.log(weak.get(key));

        const array = [{a: 1, b: 1}, {a: 2, b: 2}, {a: 3, b: 3}, {a: 4, b: 4}]

        console.log(array.findLast(n => n)); //result -> {a: 4,b: 4 }

        const sampleStrings = [
            // Examples with lone surrogates
            "igor\uD800", // Leading surrogate
            "igor\uD800komolov", // Leading surrogate followed by text
            "\uDC00yourfuse",    // Trailing surrogate
            "your\uDC00fuse",    // Trailing surrogate followed by text
            
            // Well-formed examples
            "yourFuse",       // Regular string without surrogates
            "emoji\uD83D\uDE00", // String with a complete surrogate pair (emoji)
        ];

        sampleStrings.forEach(str => {
            console.log(`Processed String: ${str.toWellFormed()}`);
        });

        // Assuming sharedArray is a SharedArrayBuffer
        const sharedArray = new Int32Array(new SharedArrayBuffer(1024));

        function performSynchronizedOperation(index, value) {
            // The waitSync method would block execution until a certain condition is met.
            // For example, it could wait until the value at the specified index is no longer equal to 0.
            Atomics.waitSync(sharedArray, index, 0);

            // Perform operations on shared memory
            sharedArray[index] = value;

            // Notify other threads or workers that the value at index has been updated
            Atomics.notify(sharedArray, index, 1);
        }

        // In a web worker or another thread
        performSynchronizedOperation(0, 123);

        const testObj = { one: 1, two: 2 };
        Object.entries(testObj).forEach(([key, value]) => {
            console.log(`key: ${key}, value: ${value}`);
        });

        Object.entries(testObj).forEach((key) => {
            console.log(`key: ${key}, value: ${value}`);
        });

        const employee = { name: 'Gary', age: 28 };
        //const { name, age } = employee;  //  If run the first two lines then it is working 
        const { name, age, company = 'Eastland' } = employee; // If run the first and last line then it is throwing an error
        var object = { myProperty: "hello"};
        var property = object && object.myProperty ? object.myProperty.big() : undefined;
        console.log(property);
    }
});
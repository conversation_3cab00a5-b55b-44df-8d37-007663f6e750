package com.sencha.tools.compressors.closure;

import com.google.javascript.jscomp.*;
import com.google.javascript.jscomp.Compiler;
import com.google.javascript.jscomp.jarjar.com.google.common.collect.ImmutableList;
import com.google.javascript.jscomp.resources.ResourceLoader;
import com.sencha.exceptions.BasicException;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compressors.BaseCompressor;
import com.sencha.tools.compressors.JsLanguageLevel;
import com.sencha.util.*;
import org.slf4j.Logger;

import java.io.File;
import java.io.IOException;
import java.io.Writer;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClosureCompressor extends BaseCompressor {
    private static final Logger _logger = SenchaLogManager.getLogger();

    private static StringBuilderWriter sourceMapWriter = new StringBuilderWriter();

    private static final String[] _disabledMessages = new String[]{
            "Non-JSDoc comment has annotations.",
            "Type annotations are not allowed here",
            "illegal use of unknown JSDoc tag",
            "Parse error. invalid param name",
            "Parse error. name not recognized due to syntax error.",
            "dangerous use of the global this",

            //type: "closure", "compression": "simple"
            "Misplaced function annotation. This JSDoc is not attached to a function node. Are you missing parentheses?",
            "Misplaced @abstract annotation. only functions or non-static methods can be abstract",
            "unreachable code",

            //type: "closure", "compression": "advanced"
            "never defined on Ext",
            "inconsistent return type",
            "Bad type annotation",
            "Missing return statement",
            "Function requires at least",
            "cannot instantiate non-constructor",
            "condition always evaluates to ",
            "does not match formal parameter",
            "never defined",
            "does not match formal parameter",
            "assignment\nfound",
            "assignment to property",
            "does not appear in ",
            "case expression doesn't match switch",
            "optional arguments must be at the end",
            "Variable referenced before declaration",
            "Variable Ext declared more than once",
            " is undeclared",
            "restricted index type",
            "case expression doesn't match switch",
            " expressions are not callable",
            "assigned a value more than once",
            "declaration of multiple variables with shared type information",
            "Redeclared variable",
            "JSDoc annotations are not supported",
            "No properties on this expression\nfound",
            "left operand\nfound",
            "left side of numeric comparison\nfound"
    };

    private boolean _compressionLevelDefined = false;
    private CompilationLevel _compressionLevel = CompilationLevel.SIMPLE_OPTIMIZATIONS;
    private boolean _warningLevelDefined = false;
    private WarningLevel _warningLevel = WarningLevel.DEFAULT;
    private boolean _transpileOnly = false;
    private boolean _includePolyfills = false;
    private boolean _forceAllPolyfills = false;

    private static String syntaxLib =
            ResourceLoader.loadTextResource(ClosureCompressor.class, "syntax-lib.js");

    @Override
    public String compress(String data) {
        StringBuilderWriter writer = new StringBuilderWriter();
        compress(data, writer);
        return writer.getBuilder().toString();
    }

    @Override
    public void compress(String data, Writer writer) {
        _logger.info("Starting Closure Compiler compression...");

        // Use the custom class loader
        ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
        try {
            ClassLoader closureClassLoader = getClosureCompilerClassLoader();
            Thread.currentThread().setContextClassLoader(closureClassLoader);

            // Check if we're using a custom JAR and temporarily move the old one
            String configuredVersion = null;
            Map<String, Object> options = getOptions();
            if (options != null && options.containsKey("version")) {
                configuredVersion = (String) options.get("version");
            }

            Path oldJarPath = null;
            Path tempOldJarPath = null;
            if (configuredVersion != null) {
                // Temporarily move the old JAR to avoid conflicts
                oldJarPath = Paths.get(Locator.getBaseDir(), "lib", "closure-compiler-v20220301.jar");
                if (Files.exists(oldJarPath)) {
                    tempOldJarPath = Paths.get(Locator.getBaseDir(), "lib", "closure-compiler-v20220301.jar.temp");
                    try {
                        Files.move(oldJarPath, tempOldJarPath);
                        _logger.info("Temporarily moved old Closure Compiler JAR to avoid conflicts");
                    } catch (Exception e) {
                        _logger.warn("Could not move old Closure Compiler JAR: " + e.getMessage());
                        tempOldJarPath = null;
                    }
                }
            }

            try {
                CompilerOptions opts = new CompilerOptions();
                opts.setPrettyPrint(false);

                WarningsGuard warnGuard = new WarningsGuard() {
                    @Override
                    public CheckLevel level(JSError jsError) {
                        for(String msg : _disabledMessages) {
                            if(jsError.getDescription().contains(msg)) {
                                return CheckLevel.OFF;
                            }
                        }
                        return jsError.getDefaultLevel();
                    }
                };

                String fileNameFrom = "compression-input", fileNameTo = null;
                Map<String, Object> filteredOpts = new HashMap<String, Object>();

                boolean loadSyntaxLib = false;

                // pre-process the options object to get high level settings processes
                // (reuse the options variable from above)
                if(options != null) {
                    for(String key : options.keySet()) {

                        if ("fileNameFrom".equals(key)) {
                            fileNameFrom = (String) options.get(key);
                        }
                        else if ("wrapLines".equals(key)) {
                            continue;
                        }
                        else if ("polyfills".equals(key)) {
                            Object val = options.get(key);
                            if (val instanceof Boolean) {
                                boolean tmp = Converter.convert(val, Boolean.class);
                                val = tmp ? "auto" : "syntax";
                            }
                            if (val instanceof String) {
                                String value = (String)val;
                                if ("all".equals(value)) {
                                    _forceAllPolyfills = true;
                                    _includePolyfills = false;
                                }
                                else if ("used".equals(value) || "auto".equals(value)) {
                                    _forceAllPolyfills = false;
                                    _includePolyfills = true;
                                }
                                else if ("none".equals(value)) {
                                    _includePolyfills = false;
                                    _forceAllPolyfills = false;
                                }
                                else if ("syntax".equals(value)) {
                                    _includePolyfills = false;
                                    _forceAllPolyfills = false;
                                    loadSyntaxLib = true;
                                }
                            }
                        }
                        else if ("transpileOnly".equals(key)) {
                            Boolean b = Converter.convert(options.get(key), Boolean.class);
                            if (b != null) {
                                setTranspileOnly(b);
                            }
                        }
                        else if ("language".equals(key)) {
                            JsLanguageLevel lvl = Converter.convert(options.get(key), JsLanguageLevel.class);
                            setOutputLanguageLevel(lvl);
                        }
                        else if ("fileNameTo".equals(key)) {
                            fileNameTo = (String) options.get(key);
                        }
                        else if ("outputCharset".equals(key) || "charset".equals(key)) {
                            String charSet = (String) options.get(key);
                            opts.setOutputCharset(Charset.forName(charSet));
                        }
                        /*
                            Process the compression options passed in app.json to process user-defined
                            compression level settings. According to Google's Closure Compiler docs the API configuration
                            parameter is defined as 'compilation_level'.

                            If the provided value is not valid, default to low compression settings (default).
                         */
                        else if ("compression".equals(key)) {

                            String _levelIn = options.get(key).toString();
                            _logger.debug("ClosureCompressor processing compilation_level with val: {} ", _levelIn);

                            // Check user defined options for compilation_level which controls compression
                            _compressionLevel = guaranteedCompilationLevelForInput(_levelIn);
                            if (isValidCompressionLevelConfig(_levelIn) && _compressionLevel != null) {

                                // User did define the compression level in the configuration
                                _compressionLevelDefined = true;
                                _logger.info("ClosureCompressor applying compilation_level: {} ", _levelIn);
                            } else {

                                // Set it back to default (simple)
                                _compressionLevel = CompilationLevel.SIMPLE_OPTIMIZATIONS;
                                _compressionLevelDefined = false;
                                _logger.warn("ClosureCompressor INVALID compilation_level: {} ", _levelIn);
                            }
                        } else if ("warningLevel".equals(key)) {
                            String _levelIn = options.get(key).toString();
                            _logger.debug("ClosureCompressor processing warning_level with val: {} ", _levelIn);

                            // Check user defined options for warning_level which controls noise
                            _warningLevel = guaranteedWarningLevelForInput(_levelIn);
                            if (isValidWarningLevelConfig(_levelIn) && _warningLevel != null) {

                                // User did define the compression level in the configuration
                                _warningLevelDefined = true;
                                _logger.info("ClosureCompressor applying warning_level: {} ", _levelIn);
                            } else {

                                // Set it back to default (simple)
                                _warningLevel = WarningLevel.DEFAULT;
                                _warningLevelDefined = false;
                                _logger.warn("ClosureCompressor INVALID warning_level: {} ", _levelIn);
                            }
                        }
                        else if (!"type".equals(key)) {
                            filteredOpts.put(key, options.get(key));
                        }
                    }
                }

                JsLanguageLevel in = getInputLanguageLevel();
                JsLanguageLevel out = getOutputLanguageLevel();

                if (in == null) {
                    in = JsLanguageLevel.NEXT;
                }

                _logger.info("JavaScript input level is {} and output level is {}", in, out);

                opts.setLanguageIn(in.getClosureLanguageMode());
                opts.setLanguageOut(out.getClosureLanguageMode());

                if (_transpileOnly) {
                    opts.setPrettyPrint(true);
                    opts.setCollapseVariableDeclarations(true);
                    opts.setQuoteKeywordProperties(true);
                    opts.setRewritePolyfills(true);
                    if (!_includePolyfills) {
                        opts.setSkipNonTranspilationPasses(true);
                    }
                } else {
                    _compressionLevel.setOptionsForCompilationLevel(opts);
                }

                // Use the user defined compression, warning options passed from app.json
                if (_warningLevelDefined) {
                    _warningLevel.setOptionsForWarningLevel(opts);
                }
                if (_compressionLevelDefined) {
                    _compressionLevel.setOptionsForCompilationLevel(opts);
                }

                // disable variable renaming due to issue with renamed variables in scopes
                // with eval() statements
                opts.setVariableRenaming(VariableRenamingPolicy.OFF);
                opts.setPropertyRenaming(PropertyRenamingPolicy.OFF);
                opts.setPreferSingleQuotes(true);
                opts.setStrictModeInput(false);
                opts.setEmitUseStrict(false);
                opts.addWarningsGuard(warnGuard);
                opts.setErrorHandler(new ErrorHandler() {
                    @Override
                    public void report(CheckLevel checkLevel, JSError jsError) {
                        //
                        // SDKTOOLS-2152: AVOIDING ALL WARNING MESSAGES
                        //
                        if (checkLevel.equals(CheckLevel.WARNING)) {
                            return;
                        }

                        // can't check for this in the warning guard, seems that this message
                        // doesn't get passed through that mechanism
                        for(String msg : _disabledMessages) {
                            if(jsError.getDescription().contains(msg)) {
                                return;
                            }
                        }
                        if (checkLevel.equals(CheckLevel.WARNING)) {
                            CompilerMessage.ClosureWarn.log(
                                    jsError.getSourceName(),
                                    jsError.getLineNumber(),
                                    jsError.getCharno(),
                                    jsError.getDescription());
                        } else if (checkLevel.equals(CheckLevel.ERROR)) {
                            CompilerMessage.ClosureError.log(
                                    jsError.getSourceName(),
                                    jsError.getLineNumber(),
                                    jsError.getCharno(),
                                    jsError.getDescription());
                        }
                    }
                });
                opts.setOutputCharset(Charset.forName("UTF-16"));
                opts.setSummaryDetailLevel(0);
                opts.setVariableRenaming("LOCAL");
//            opts.setDefineReplacements(new HashMap<String, Object>(){{
//                put("Ext.baseCSSPrefix", "x-");
//            }});


                // if options have been supplied, set them on the closure options object now.
                BeanInfoCache beans = BeanInfoCache.getInstance();
                for(String key : filteredOpts.keySet()) {
                    Object value = filteredOpts.get(key);
                    _logger.debug("Setting compressor option : {} to {}", key, value);
                    BeanInfoCache.Property prop = beans.getProperty(opts, key);
                    if (prop == null) {
                        _logger.warn("invalid app.json configuration: Could not set bean property named {} on options object of type : {}", key, opts.getClass().getName());
                    } else {
                        prop.writeValue(opts, value);
                    }
                }

                // ensure array prototype polyfill is deleted to prevent issues with xtemplates
                if ((_includePolyfills || _forceAllPolyfills) && !data.contains("$jscomp")) {
                    data =
                            "try { \n" +
                                    "    if (Array.prototype.values.toString().indexOf(\"[native code]\") == -1) {\n" +
                                    "        delete Array.prototype.values; \n" +
                                    "    }\n" +
                                    "} \n" +
                                    "catch (e) {}\n" + data;
                }
                // Load SourceFile and other classes using reflection from the custom class loader
                ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
                Class<?> sourceFileClass = currentClassLoader.loadClass("com.google.javascript.jscomp.SourceFile");
                Class<?> commandLineRunnerClass = currentClassLoader.loadClass("com.google.javascript.jscomp.CommandLineRunner");
                Class<?> compilerClass = currentClassLoader.loadClass("com.google.javascript.jscomp.Compiler");

                // Create SourceFile using reflection
                Method fromCodeMethod = sourceFileClass.getMethod("fromCode", String.class, String.class);
                Object sf = fromCodeMethod.invoke(null, fileNameFrom, data);

                // Get default externs using reflection
                Method getDefaultExternsMethod = commandLineRunnerClass.getMethod("getDefaultExterns");
                List<?> externs = (List<?>) getDefaultExternsMethod.invoke(null);

                List<Object> sources = Arrays.asList(sf);

                // Create Compiler using reflection
                Object compiler = compilerClass.getDeclaredConstructor().newInstance();

                // Log the actual Closure Compiler version being used
                try {
                    String compilerVersion = compiler.getClass().getPackage().getImplementationVersion();
                    if (compilerVersion != null) {
                        _logger.info("Closure Compiler implementation version: " + compilerVersion);
                    } else {
                        _logger.info("Closure Compiler version information not available in package");
                    }
                    _logger.info("Closure Compiler class loaded from: " + compiler.getClass().getProtectionDomain().getCodeSource().getLocation());
                } catch (Exception e) {
                    _logger.debug("Could not determine Closure Compiler version: " + e.getMessage());
                }

                // Load ErrorManager and related classes using reflection
                Class<?> errorManagerClass = currentClassLoader.loadClass("com.google.javascript.jscomp.ErrorManager");
                Class<?> checkLevelClass = currentClassLoader.loadClass("com.google.javascript.jscomp.CheckLevel");
                Class<?> jsErrorClass = currentClassLoader.loadClass("com.google.javascript.jscomp.JSError");
                Class<?> immutableListClass = currentClassLoader.loadClass("com.google.javascript.jscomp.jarjar.com.google.common.collect.ImmutableList");

                // Create a custom ErrorManager using reflection
                Object errorManager = java.lang.reflect.Proxy.newProxyInstance(
                    currentClassLoader,
                    new Class<?>[] { errorManagerClass },
                    (proxy, method, args) -> {
                        switch (method.getName()) {
                            case "report":
                            case "generateReport":
                            case "setTypedPercent":
                                return null;
                            case "getErrorCount":
                            case "getWarningCount":
                                return 0;
                            case "getTypedPercent":
                                return 0.0;
                            case "getErrors":
                            case "getWarnings":
                                // Return empty ImmutableList
                                Method builderMethod = immutableListClass.getMethod("builder");
                                Object builder = builderMethod.invoke(null);
                                Method buildMethod = builder.getClass().getMethod("build");
                                return buildMethod.invoke(builder);
                            default:
                                return null;
                        }
                    }
                );

                // Set error manager using reflection
                Method setErrorManagerMethod = compilerClass.getMethod("setErrorManager", errorManagerClass);
                setErrorManagerMethod.invoke(compiler, errorManager);

                // Disable threads using reflection
                Method disableThreadsMethod = compilerClass.getMethod("disableThreads");
                disableThreadsMethod.invoke(compiler);

                if (_forceAllPolyfills && !data.contains("$jscomp")) {
                    // Use reflection for all compiler method calls
                    Method initMethod = compilerClass.getMethod("init", List.class, List.class, opts.getClass());
                    initMethod.invoke(compiler, externs, sources, opts);

                    Method ensureLibraryInjectedMethod = ReflectionUtil.getMethod(compiler, "ensureLibraryInjected", String.class, boolean.class);
                    ReflectionUtil.call(compiler, ensureLibraryInjectedMethod, "es6_runtime", true);

                    try {
                        Method hasErrorsMethod = compilerClass.getMethod("hasErrors");

                        if (!(Boolean) hasErrorsMethod.invoke(compiler)) {
                            Method parseForCompilationMethod = compilerClass.getMethod("parseForCompilation");
                            parseForCompilationMethod.invoke(compiler);
                        }

                        if (!(Boolean) hasErrorsMethod.invoke(compiler)) {
                            if (opts.getInstrumentForCoverageOnly()) {
                                Method instrumentForCoverageMethod = compilerClass.getMethod("instrumentForCoverage");
                                instrumentForCoverageMethod.invoke(compiler);
                            } else {
                                Method stage1PassesMethod = compilerClass.getMethod("stage1Passes");
                                stage1PassesMethod.invoke(compiler);

                                if (!(Boolean) hasErrorsMethod.invoke(compiler)) {
                                    Method stage2PassesMethod = compilerClass.getMethod("stage2Passes");
                                    stage2PassesMethod.invoke(compiler);
                                }
                            }
                            Method performPostCompilationTasksMethod = compilerClass.getMethod("performPostCompilationTasks");
                            performPostCompilationTasksMethod.invoke(compiler);
                        }
                    } finally {
                        Method generateReportMethod = compilerClass.getMethod("generateReport");
                        generateReportMethod.invoke(compiler);
                    }
                }
                else {
                    Method compileMethod = compilerClass.getMethod("compile", List.class, List.class, opts.getClass());
                    compileMethod.invoke(compiler, externs, sources, opts);
                }

                if (loadSyntaxLib) {
                    writer.write(syntaxLib);
                    writer.write(StringUtil.NewLine);
                }

                // Get compiled source using reflection
                Method toSourceMethod = compilerClass.getMethod("toSource");
                String compiledSource = (String) toSourceMethod.invoke(compiler);
                writer.write(compiledSource);

                // Get source map using reflection
                Method getSourceMapMethod = compilerClass.getMethod("getSourceMap");
                Object sourceMap = getSourceMapMethod.invoke(compiler);
                if (sourceMap != null) {
                    Method appendToMethod = sourceMap.getClass().getMethod("appendTo", Appendable.class, String.class);
                    appendToMethod.invoke(sourceMap, sourceMapWriter, fileNameTo);
                }
            } catch (Exception e) {
                throw BasicException.raise(e);
            } finally {
                // Restore the old JAR if it was moved
                if (tempOldJarPath != null && oldJarPath != null) {
                    try {
                        Files.move(tempOldJarPath, oldJarPath);
                        _logger.info("Restored old Closure Compiler JAR");
                    } catch (Exception e) {
                        _logger.warn("Could not restore old Closure Compiler JAR: " + e.getMessage());
                    }
                }
            }
        } finally {
            // Restore the original class loader
            Thread.currentThread().setContextClassLoader(originalClassLoader);
        }
    }

    public String getSourceMap() {
        String sm = sourceMapWriter.getBuilder().toString();
        sourceMapWriter.resetBuilder();
        return sm;
    }

    public boolean isTranspileOnly() {
        return _transpileOnly;
    }

    public void setTranspileOnly(boolean transpileOnly) {
        _transpileOnly = transpileOnly;
        if(_transpileOnly) {
            _forceAllPolyfills = true;
            _includePolyfills = false;
        }
    }

    public boolean isIncludePolyfills() {
        return _includePolyfills;
    }

    public void setIncludePolyfills(boolean includePolyfills) {
        _includePolyfills = includePolyfills;
        if (!_includePolyfills) {
            _forceAllPolyfills = false;
        }
    }

    public boolean isForceAllPolyfills() {
        return _forceAllPolyfills;
    }

    public void setForceAllPolyfills(boolean forceAllPolyfills) {
        _forceAllPolyfills = forceAllPolyfills;
        if (forceAllPolyfills) {
            _includePolyfills = false;
        }
    }

    private Boolean isValidCompressionLevelConfig(String input) {
        input = input.toUpperCase();
        return (CompilationLevel.WHITESPACE_ONLY.toString().startsWith(input) ||
                CompilationLevel.SIMPLE_OPTIMIZATIONS.toString().startsWith(input) ||
                CompilationLevel.ADVANCED_OPTIMIZATIONS.toString().startsWith(input));
    }

    private CompilationLevel guaranteedCompilationLevelForInput(String input) {
        input = input.toUpperCase();
        if (CompilationLevel.WHITESPACE_ONLY.toString().startsWith(input)) {
            return CompilationLevel.WHITESPACE_ONLY;
        }
        if (CompilationLevel.SIMPLE_OPTIMIZATIONS.toString().startsWith(input)) {
            return CompilationLevel.SIMPLE_OPTIMIZATIONS;
        }
        if (CompilationLevel.ADVANCED_OPTIMIZATIONS.toString().startsWith(input)) {
            return CompilationLevel.ADVANCED_OPTIMIZATIONS;
        }
        return CompilationLevel.SIMPLE_OPTIMIZATIONS; // Default
    }

    private Boolean isValidWarningLevelConfig(String input) {
        input = input.toUpperCase();
        return (WarningLevel.QUIET.toString().startsWith(input) ||
                WarningLevel.DEFAULT.toString().startsWith(input) ||
                WarningLevel.VERBOSE.toString().startsWith(input));
    }

    private WarningLevel guaranteedWarningLevelForInput(String input) {
        input = input.toUpperCase();
        if (WarningLevel.QUIET.toString().startsWith(input)) {
            return WarningLevel.QUIET;
        }
        if (WarningLevel.DEFAULT.toString().startsWith(input)) {
            return WarningLevel.DEFAULT;
        }
        if (WarningLevel.VERBOSE.toString().startsWith(input)) {
            return WarningLevel.VERBOSE;
        }
        return WarningLevel.DEFAULT;
    }

    private ClassLoader getClosureCompilerClassLoader() {
        try {
            // First check if a specific version is configured
            String configuredVersion = null;
            Map<String, Object> options = getOptions();
            if (options != null && options.containsKey("version")) {
                configuredVersion = (String) options.get("version");
            }

            Path jarPath;
            if (configuredVersion != null) {
                // Use the specified version
                jarPath = ClosureCompressorManager.getClosureCompilerJar(configuredVersion);
                if (!Files.exists(jarPath)) {
                    _logger.warn("Configured Closure Compiler version " + configuredVersion +
                             " not found. Using default version.");
                    jarPath = ClosureCompressorManager.getDefaultClosureCompilerJar();
                    _logger.info("Using default Closure Compiler JAR: " + jarPath);
                } else {
                    _logger.info("Using configured Closure Compiler version " + configuredVersion + " from: " + jarPath);
                }
            } else {
                // Use the default version
                jarPath = ClosureCompressorManager.getDefaultClosureCompilerJar();
                _logger.info("No version configured, using default Closure Compiler JAR: " + jarPath);
            }

            // If the JAR exists, create a URLClassLoader for it
            if (Files.exists(jarPath)) {
                URL[] urls = new URL[] { jarPath.toUri().toURL() };
                // Create a custom class loader that prioritizes the specific JAR
                URLClassLoader classLoader = new URLClassLoader(urls, ClassLoader.getSystemClassLoader().getParent()) {
                    @Override
                    public Class<?> loadClass(String name) throws ClassNotFoundException {
                        // For Closure Compiler classes, load from our JAR first
                        if (name.startsWith("com.google.javascript.jscomp")) {
                            try {
                                return findClass(name);
                            } catch (ClassNotFoundException e) {
                                // Fall back to parent if not found in our JAR
                            }
                        }
                        return super.loadClass(name);
                    }
                };
                _logger.info("Created custom URLClassLoader for Closure Compiler JAR: " + jarPath);
                return classLoader;
            } else {
                _logger.warn("Closure Compiler JAR not found at: " + jarPath);
            }
        } catch (Exception e) {
            _logger.warn("Error loading Closure Compiler: " + e.getMessage());
        }

        // Fall back to the default class loader
        _logger.info("Falling back to default class loader for Closure Compiler");
        return getClass().getClassLoader();
    }
}
